# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# Licensed under the 【火山方舟】原型应用软件自用许可协议

"""
本地知识库实现 - 替代火山引擎知识库
"""

import json
import os
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any, Tuple
import config
from arkitect.types.llm.model import ActionDetail, ArkMessage, ToolDetail


class LocalKnowledgeBase:
    """本地知识库实现"""
    
    def __init__(self):
        self.knowledge_path = Path(config.local_knowledge_path) if config.use_proxy_api else None
        self.faq_path = Path(config.local_faq_path) if config.use_proxy_api else None
        
        # 初始化知识库
        if config.use_proxy_api:
            self._init_knowledge_base()
    
    def _init_knowledge_base(self):
        """初始化本地知识库"""
        
        # 创建知识库目录
        self.knowledge_path.mkdir(parents=True, exist_ok=True)
        self.faq_path.mkdir(parents=True, exist_ok=True)
        
        # 初始化商品知识库
        self._init_product_knowledge()
        
        # 初始化FAQ知识库
        self._init_faq_knowledge()
    
    def _init_product_knowledge(self):
        """初始化商品知识库"""
        from data.product import get_products
        
        products = get_products()
        knowledge_file = self.knowledge_path / "products.json"
        
        # 构建商品知识库数据
        knowledge_data = []
        for product_name, product_info in products.items():
            knowledge_data.append({
                "product_name": product_name,
                "description": product_info["description"],
                "content": f"商品名称：{product_name}\n商品描述：{product_info['description']}",
                "metadata": {
                    "product_name": product_name,
                    "account_id": "test"
                }
            })
        
        # 保存到本地文件
        with open(knowledge_file, 'w', encoding='utf-8') as f:
            json.dump(knowledge_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 商品知识库已初始化: {knowledge_file}")
    
    def _init_faq_knowledge(self):
        """初始化FAQ知识库"""
        faq_file = self.faq_path / "faq.json"
        
        # 默认FAQ数据
        default_faq = [
            {
                "question": "如何查询订单？",
                "answer": "亲，您可以提供订单号，我来帮您查询订单状态哦~",
                "score": 5,
                "metadata": {
                    "account_id": "test"
                }
            },
            {
                "question": "退货退款流程",
                "answer": "亲，我们支持7天无理由退货，您可以在订单页面申请退款，我们会尽快处理~",
                "score": 5,
                "metadata": {
                    "account_id": "test"
                }
            },
            {
                "question": "物流查询",
                "answer": "亲，请提供您的订单号或快递单号，我来帮您查询物流信息~",
                "score": 5,
                "metadata": {
                    "account_id": "test"
                }
            }
        ]
        
        # 保存到本地文件
        with open(faq_file, 'w', encoding='utf-8') as f:
            json.dump(default_faq, f, ensure_ascii=False, indent=2)
        
        print(f"✅ FAQ知识库已初始化: {faq_file}")
    
    def search_knowledge(
        self,
        query: str,
        doc_filter: Dict[str, Any],
        limit: int = 3
    ) -> Dict[str, Any]:
        """搜索商品知识库"""
        
        if not config.use_proxy_api:
            # 如果不是中转API模式，返回空结果
            return {"result_list": []}
        
        knowledge_file = self.knowledge_path / "products.json"
        
        if not knowledge_file.exists():
            return {"result_list": []}
        
        # 加载知识库数据
        with open(knowledge_file, 'r', encoding='utf-8') as f:
            knowledge_data = json.load(f)
        
        # 简单的关键词匹配搜索
        results = []
        query_lower = query.lower()
        
        for item in knowledge_data:
            # 检查是否匹配过滤条件
            if self._match_filter(item["metadata"], doc_filter):
                # 简单的文本匹配
                content_lower = item["content"].lower()
                if query_lower in content_lower or any(word in content_lower for word in query_lower.split()):
                    results.append({
                        "content": item["content"],
                        "score": 0.8,  # 固定分数
                        "doc_info": {
                            "doc_name": item["product_name"],
                            "doc_id": f"product_{item['product_name']}"
                        }
                    })
        
        # 限制结果数量
        results = results[:limit]
        
        return {
            "result_list": results,
            "rewrite_query": query
        }
    
    def search_faq(
        self,
        query: str,
        doc_filter: Dict[str, Any],
        limit: int = 5
    ) -> Dict[str, Any]:
        """搜索FAQ知识库"""
        
        if not config.use_proxy_api:
            return {"result_list": []}
        
        faq_file = self.faq_path / "faq.json"
        
        if not faq_file.exists():
            return {"result_list": []}
        
        # 加载FAQ数据
        with open(faq_file, 'r', encoding='utf-8') as f:
            faq_data = json.load(f)
        
        # 简单的关键词匹配搜索
        results = []
        query_lower = query.lower()
        
        for item in faq_data:
            # 检查是否匹配过滤条件
            if self._match_filter(item["metadata"], doc_filter):
                # 简单的文本匹配
                question_lower = item["question"].lower()
                answer_lower = item["answer"].lower()
                
                if (query_lower in question_lower or 
                    query_lower in answer_lower or 
                    any(word in question_lower or word in answer_lower for word in query_lower.split())):
                    
                    results.append({
                        "content": f"问题：{item['question']}\n答案：{item['answer']}",
                        "score": item.get("score", 5) / 5.0,
                        "doc_info": {
                            "doc_name": f"FAQ_{item['question'][:20]}",
                            "doc_id": f"faq_{hash(item['question'])}"
                        }
                    })
        
        # 按分数排序并限制结果数量
        results.sort(key=lambda x: x["score"], reverse=True)
        results = results[:limit]
        
        return {
            "result_list": results,
            "rewrite_query": query
        }
    
    def _match_filter(self, metadata: Dict[str, Any], doc_filter: Dict[str, Any]) -> bool:
        """检查是否匹配过滤条件"""
        
        if not doc_filter:
            return True
        
        # 简化的过滤逻辑
        if "op" in doc_filter and doc_filter["op"] == "or":
            for cond in doc_filter.get("conds", []):
                if self._match_condition(metadata, cond):
                    return True
            return False
        
        return True
    
    def _match_condition(self, metadata: Dict[str, Any], condition: Dict[str, Any]) -> bool:
        """检查单个条件"""
        
        if condition.get("op") == "must":
            field = condition.get("field")
            values = condition.get("conds", [])
            
            if field in metadata:
                metadata_value = metadata[field]
                if isinstance(metadata_value, list):
                    return any(val in metadata_value for val in values)
                else:
                    return metadata_value in values
        
        return True
    
    def save_faq(self, faq_data: pd.DataFrame, account_id: str):
        """保存FAQ到本地"""
        
        if not config.use_proxy_api:
            print("⚠️  当前模式不支持本地FAQ保存")
            return
        
        faq_file = self.faq_path / "faq.json"
        
        # 加载现有FAQ
        existing_faq = []
        if faq_file.exists():
            with open(faq_file, 'r', encoding='utf-8') as f:
                existing_faq = json.load(f)
        
        # 添加新FAQ
        for _, row in faq_data.iterrows():
            new_faq = {
                "question": row["question"],
                "answer": row["answer"],
                "score": row["score"],
                "metadata": {
                    "account_id": account_id
                }
            }
            existing_faq.append(new_faq)
        
        # 保存更新后的FAQ
        with open(faq_file, 'w', encoding='utf-8') as f:
            json.dump(existing_faq, f, ensure_ascii=False, indent=2)
        
        print(f"✅ FAQ已保存到本地: {faq_file}")


# 全局实例
local_kb = LocalKnowledgeBase()


def retrieval_knowledge(
    messages: List[ArkMessage], 
    doc_filter: dict
) -> Tuple[str, ActionDetail]:
    """知识检索函数 - 兼容原版接口"""
    
    if config.use_proxy_api:
        # 使用本地知识库
        query = messages[-1].content
        
        # 搜索商品知识库
        knowledge_results = local_kb.search_knowledge(query, doc_filter, limit=3)
        
        # 搜索FAQ知识库
        faq_results = local_kb.search_faq(query, doc_filter, limit=5)
        
        # 合并结果
        all_results = knowledge_results.get("result_list", []) + faq_results.get("result_list", [])
        
        ref = [result.get("doc_info", {}) for result in all_results]
        
        action_detail = ActionDetail(
            name="local_knowledge",
            tool_details=[
                ToolDetail(
                    name="local_retrieval",
                    input=query,
                    output=ref,
                )
            ],
        )
        
        return (
            f"""
# {"参考资料" if config.language == "zh" else "References"}
<context>
{all_results}
</context>
""",
            action_detail,
        )
    else:
        # 使用原版火山引擎知识库
        from data.rag import retrieval_knowledge as original_retrieval
        return original_retrieval(messages, doc_filter)
