# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# Licensed under the 【火山方舟】原型应用软件自用许可协议
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     https://www.volcengine.com/docs/82379/1433703
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import os
from pathlib import Path

# 加载.env文件
try:
    from dotenv import load_dotenv
    # 查找.env文件路径
    env_path = Path(__file__).parent / '.env'
    if env_path.exists():
        load_dotenv(env_path)
        print(f"✅ 已加载配置文件: {env_path}")
    else:
        print(f"⚠️  未找到.env文件: {env_path}")
except ImportError:
    print("⚠️  未安装python-dotenv，使用系统环境变量")

# ===========================================
# API配置模式选择
# ===========================================
# 检查是否使用中转API
use_proxy_api = bool(os.getenv("ECOVAI_API_KEY"))

if use_proxy_api:
    print("🔄 使用中转API模式 (ECOVAI)")
    # 中转API配置
    api_key = os.getenv("ECOVAI_API_KEY", "")
    api_base = os.getenv("ECOVAI_API_BASE", "https://api.ecovai.cn/v1")
    model_name = os.getenv("ECOVAI_MODEL", "DeepSeek-V3-0324")

    # 中转API模式下的配置
    endpoint_id = model_name
    use_server_auth = True
else:
    print("🏔️  使用火山引擎原版模式")
    # 火山引擎配置
    ak = os.getenv("VOLC_ACCESSKEY", "")
    sk = os.getenv("VOLC_SECRETKEY", "")
    endpoint_id = os.getenv("LLM_ENDPOINT_ID", "doubao-seed-1-6-250615")
    use_server_auth = os.getenv("USE_SERVER_AUTH", "False").lower() in ("true", "1", "t")

# ===========================================
# 通用配置
# ===========================================
# 知识库配置
collection_name = os.getenv("COLLECTION_NAME", "")
faq_collection_name = os.getenv("FAQ_COLLECTION_NAME", "")

# 存储配置
bucket_name = os.getenv("BUCKET_NAME", "")

# 语言和端口配置
language = os.getenv("LANGUAGE", "zh")  # 'zh' for Chinese, 'en' for English
port = int(os.getenv("PORT", "8080"))

# ===========================================
# 替代方案配置 (中转API模式)
# ===========================================
if use_proxy_api:
    # 本地知识库路径
    local_knowledge_path = os.getenv("LOCAL_KNOWLEDGE_PATH", "./data/knowledge")
    local_faq_path = os.getenv("LOCAL_FAQ_PATH", "./data/faq")
    local_storage_path = os.getenv("LOCAL_STORAGE_PATH", "./data/storage")

    # 创建本地目录
    for path in [local_knowledge_path, local_faq_path, local_storage_path]:
        Path(path).mkdir(parents=True, exist_ok=True)

# ===========================================
# 配置验证
# ===========================================
def validate_config():
    """验证配置完整性"""
    errors = []

    if use_proxy_api:
        if not api_key:
            errors.append("❌ ECOVAI_API_KEY 未设置")
        if not api_base:
            errors.append("❌ ECOVAI_API_BASE 未设置")
        if not model_name:
            errors.append("❌ ECOVAI_MODEL 未设置")
    else:
        if not ak:
            errors.append("❌ VOLC_ACCESSKEY 未设置")
        if not sk:
            errors.append("❌ VOLC_SECRETKEY 未设置")
        if not collection_name:
            errors.append("❌ COLLECTION_NAME 未设置")
        if not faq_collection_name:
            errors.append("❌ FAQ_COLLECTION_NAME 未设置")
        if not bucket_name:
            errors.append("❌ BUCKET_NAME 未设置")

    if errors:
        print("\n".join(errors))
        print("\n💡 请检查.env文件配置或环境变量设置")
        return False

    print("✅ 配置验证通过")
    return True

# 启动时验证配置
if __name__ == "__main__":
    validate_config()
