# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# Licensed under the 【火山方舟】原型应用软件自用许可协议
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#     https://www.volcengine.com/docs/82379/1433703
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import os
from typing import AsyncIterable, List, Union

import pandas as pd
from volcenginesdkarkruntime import AsyncArk
import config
from data import rag
from data.product import get_products
from fastapi import HTTPException
from next_question import next_question_chat
from pydantic import BaseModel, Field
from quality_inspection import quality_inspection_chat
from summary import summary_chat
from tools.tools import FUNCTION_MAP, register_support_functions
from utils import get_auth_header, get_handler

# 根据配置选择知识检索方式
if config.use_proxy_api:
    from local_knowledge import retrieval_knowledge
    print("🔄 使用本地知识库")
else:
    from data.rag import retrieval_knowledge
    print("🏔️  使用火山引擎知识库")

# 根据配置选择LLM客户端
if config.use_proxy_api:
    from llm_client import get_llm_client
    print("🔄 使用中转API客户端")
else:
    from arkitect.core.component.llm import BaseChatLanguageModel as get_llm_client
    print("🏔️  使用火山引擎LLM客户端")

from arkitect.core.component.bot.server import BotServer
from arkitect.core.component.llm import BaseChatLanguageModel
from arkitect.core.errors import InternalServiceError
from arkitect.launcher.runner import (
    get_endpoint_config,
    get_runner,
)
from arkitect.telemetry.trace import task
from arkitect.telemetry.trace.setup import setup_tracing
from arkitect.types.llm.model import (
    ArkChatCompletionChunk,
    ArkChatRequest,
    ArkChatResponse,
    ArkMessage,
    BotUsage,
)
from arkitect.utils.context import (
    set_resource_id,
    set_resource_type,
)


@task()
async def custom_support_chat(
    request: ArkChatRequest,
) -> AsyncIterable[Union[ArkChatCompletionChunk, ArkChatResponse]]:
    meta_data = request.metadata if request.metadata else {}
    account_id = meta_data.get("account_id", "test")
    functions = meta_data.get(
        "support_functions",
        [*FUNCTION_MAP],
    )
    products = meta_data.get("product_list", [*get_products()])

    # insert knowledge
    tools, system_prompt = register_support_functions(functions, products, account_id)
    messages = [ArkMessage(role="system", content=system_prompt)]
    messages.extend(request.messages)
    knowledge_prompt, action_detail = retrieval_knowledge(
        messages,
        {
            "op": "or",
            "conds": [
                {"op": "must", "field": "account_id", "conds": [account_id]},
                {
                    "op": "must",
                    "field": "产品名" if config.language == "zh" else "product_name",
                    "conds": products,
                },
            ],
        },
    )

    # 根据配置选择LLM客户端
    if config.use_proxy_api:
        llm = get_llm_client()
    else:
        llm = get_llm_client(
            model=config.endpoint_id,
            messages=messages,
        )

    if request.stream:
        if config.use_proxy_api:
            # 中转API流式调用
            async for resp in llm.astream(
                messages=messages,
                functions=tools,
                additional_system_prompts=[knowledge_prompt],
            ):
                if hasattr(resp, 'usage') and resp.usage:
                    resp.bot_usage = BotUsage(action_details=[action_detail])
                yield resp
        else:
            # 火山引擎流式调用
            async for resp in llm.astream(
                functions=tools,
                additional_system_prompts=[knowledge_prompt],
                extra_headers=get_auth_header(),
                extra_body={"thinking": {"type": "disabled"}},
            ):
                if resp.usage:
                    resp.bot_usage = BotUsage(action_details=[action_detail])
                yield resp
    else:
        if config.use_proxy_api:
            # 中转API非流式调用
            resp = await llm.arun(
                messages=messages,
                functions=tools,
                additional_system_prompts=[knowledge_prompt],
            )
        else:
            # 火山引擎非流式调用
            resp = await llm.arun(
                functions=tools,
                additional_system_prompts=[knowledge_prompt],
                extra_headers=get_auth_header(),
                extra_body={"thinking": {"type": "disabled"}},
            )
        resp.bot_usage = BotUsage(action_details=[action_detail])
        yield resp


class Product(BaseModel):
    name: str
    description: str
    cover_image: str


class ProductListResponse(BaseModel):
    products: List[Product]
    total: int


async def list_products():
    products_dict = get_products()
    return ProductListResponse(
        products=[Product(**v) for v in products_dict.values()],
        total=len(products_dict),
    )


class FAQRequest(BaseModel):
    question: str = Field(..., max_length=100)
    answer: str = Field(..., max_length=500)
    score: int = Field(..., ge=1, le=5)
    account_id: str = Field(..., max_length=100)


async def save_faq(faq: FAQRequest):
    columns_order = ["question", "answer", "score"]
    try:
        if config.use_proxy_api:
            # 使用本地知识库保存FAQ
            from local_knowledge import local_kb
            local_kb.save_faq(
                pd.DataFrame(
                    [{"question": faq.question, "answer": faq.answer, "score": faq.score}],
                    columns=columns_order,
                ),
                faq.account_id,
            )
        else:
            # 使用火山引擎保存FAQ
            rag.save_faq(
                pd.DataFrame(
                    [{"question": faq.question, "answer": faq.answer, "score": faq.score}],
                    columns=columns_order,
                ),
                faq.account_id,
            )
    except Exception as e:
        err = InternalServiceError(str(e))
        raise HTTPException(
            status_code=err.http_code,
            detail=err.to_error().model_dump(exclude_none=True, exclude_unset=True),
        )
    return {"message": "success"}


if __name__ == "__main__":
    # 验证配置
    if not config.validate_config():
        exit(1)

    port = os.getenv("_FAAS_RUNTIME_PORT") or config.port
    set_resource_type(os.getenv("RESOURCE_TYPE") or "")
    set_resource_id(os.getenv("RESOURCE_ID") or "")

    setup_tracing(endpoint=os.getenv("TRACE_ENDPOINT"), trace_on=False)

    # 根据配置选择客户端
    clients = {}
    if not config.use_proxy_api:
        clients["ark"] = (
            AsyncArk,
            {
                "base_url": "https://ark.cn-beijing.volces.com/api/v3"
                if config.language == "zh"
                else "https://ark.ap-southeast.volces.com/api/v3",
                "region": "cn-beijing" if config.language == "zh" else "ap-southeast-1",
            },
        )

    server: BotServer = BotServer(
        runner=get_runner(custom_support_chat),
        health_check_path="/v1/ping",
        endpoint_config=get_endpoint_config(
            "/api/v3/bots/chat/completions", custom_support_chat
        ),
        clients=clients,
    )
    server.app.add_api_route(
        "/api/v3/bots/chat/completions",
        get_handler(custom_support_chat),
        methods=["POST"],
    )
    server.app.add_api_route(
        "/api/v3/bots/chat/completions/products",
        list_products,
        methods=["GET"],
        response_model=ProductListResponse,
    )
    server.app.add_api_route(
        "/api/v3/bots/chat/completions/save_faq", save_faq, methods=["POST"]
    )
    server.app.add_api_route(
        "/api/v3/bots/chat/completions/summary",
        get_handler(summary_chat),
        methods=["POST"],
    )
    server.app.add_api_route(
        "/api/v3/bots/chat/completions/quality_inspection",
        get_handler(quality_inspection_chat),
        methods=["POST"],
    )
    server.app.add_api_route(
        "/api/v3/bots/chat/completions/next_question",
        get_handler(next_question_chat),
        methods=["POST"],
    )
    server.run(app=server.app, port=int(port), host="0.0.0.0")

    print(f"🚀 服务已启动在端口 {port}")
    print(f"📋 配置模式: {'中转API' if config.use_proxy_api else '火山引擎'}")
    print(f"🌍 语言设置: {config.language}")
    print(f"📖 API文档: http://localhost:{port}/docs")
