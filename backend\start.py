#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能客服助手启动脚本
支持中转API和火山引擎两种模式
"""

import os
import sys
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import config
        print("✅ 配置模块加载成功")
        
        if config.use_proxy_api:
            print("🔄 检测到中转API模式")
            try:
                import openai
                print("✅ OpenAI客户端已安装")
            except ImportError:
                print("❌ 缺少openai依赖，请运行: pip install openai")
                return False
        else:
            print("🏔️  检测到火山引擎模式")
            try:
                import volcengine
                print("✅ 火山引擎SDK已安装")
            except ImportError:
                print("❌ 缺少volcengine依赖，请运行: pip install volcengine")
                return False
        
        try:
            from dotenv import load_dotenv
            print("✅ python-dotenv已安装")
        except ImportError:
            print("❌ 缺少python-dotenv依赖，请运行: pip install python-dotenv")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 依赖检查失败: {e}")
        return False

def setup_environment():
    """设置环境"""
    
    # 检查.env文件
    env_file = Path(__file__).parent / '.env'
    env_example = Path(__file__).parent / '.env.example'
    
    if not env_file.exists():
        if env_example.exists():
            print(f"⚠️  未找到.env文件，请复制 {env_example} 为 {env_file} 并配置相关参数")
            
            # 询问是否自动复制
            try:
                response = input("是否自动复制.env.example为.env? (y/N): ").strip().lower()
                if response in ['y', 'yes']:
                    import shutil
                    shutil.copy(env_example, env_file)
                    print(f"✅ 已复制配置文件到 {env_file}")
                    print("⚠️  请编辑.env文件并配置相关参数后重新启动")
                    return False
            except KeyboardInterrupt:
                print("\n👋 启动已取消")
                return False
        else:
            print(f"❌ 未找到配置文件 {env_file} 或 {env_example}")
            return False
    
    return True

def print_startup_info():
    """打印启动信息"""
    import config
    
    print("\n" + "="*50)
    print("🤖 智能客服助手")
    print("="*50)
    
    if config.use_proxy_api:
        print("🔄 模式: 中转API")
        print(f"🔗 API地址: {config.api_base}")
        print(f"🤖 模型: {config.model_name}")
        print(f"📚 知识库: 本地模式")
    else:
        print("🏔️  模式: 火山引擎")
        print(f"🤖 模型: {config.endpoint_id}")
        print(f"📚 商品知识库: {config.collection_name}")
        print(f"❓ FAQ知识库: {config.faq_collection_name}")
    
    print(f"🌍 语言: {config.language}")
    print(f"🚀 端口: {config.port}")
    print("="*50)

def main():
    """主函数"""
    
    print("🚀 正在启动智能客服助手...")
    
    # 检查环境设置
    if not setup_environment():
        sys.exit(1)
    
    # 检查依赖
    if not check_dependencies():
        print("\n💡 请安装缺失的依赖后重新启动")
        sys.exit(1)
    
    # 打印启动信息
    print_startup_info()
    
    # 启动服务
    try:
        print("\n🔄 正在启动服务...")
        from main import main as start_server
        start_server()
        
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
