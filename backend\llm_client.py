# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# Licensed under the 【火山方舟】原型应用软件自用许可协议

"""
LLM客户端适配器 - 支持火山引擎和中转API
"""

import json
import asyncio
from typing import AsyncIterable, List, Dict, Any, Union, Optional
from openai import AsyncOpenAI
import config
from arkitect.core.component.llm import BaseChatLanguageModel
from arkitect.types.llm.model import (
    ArkChatCompletionChunk,
    ArkChatResponse,
    ArkMessage,
    BotUsage,
)


class ProxyLLMClient:
    """中转API客户端"""
    
    def __init__(self):
        if not config.use_proxy_api:
            raise ValueError("当前配置不支持中转API模式")
        
        self.client = AsyncOpenAI(
            api_key=config.api_key,
            base_url=config.api_base,
        )
        self.model = config.model_name
    
    async def astream(
        self,
        messages: List[ArkMessage],
        functions: Optional[List] = None,
        additional_system_prompts: Optional[List[str]] = None,
        **kwargs
    ) -> AsyncIterable[Union[ArkChatCompletionChunk, ArkChatResponse]]:
        """流式对话"""
        
        # 转换消息格式
        openai_messages = self._convert_messages(messages, additional_system_prompts)
        
        # 构建请求参数
        request_params = {
            "model": self.model,
            "messages": openai_messages,
            "stream": True,
            "temperature": 0.7,
        }
        
        # 添加函数调用支持
        if functions:
            request_params["tools"] = self._convert_functions(functions)
            request_params["tool_choice"] = "auto"
        
        try:
            # 调用中转API
            stream = await self.client.chat.completions.create(**request_params)
            
            async for chunk in stream:
                # 转换响应格式
                ark_chunk = self._convert_chunk_response(chunk)
                if ark_chunk:
                    yield ark_chunk
                    
        except Exception as e:
            print(f"❌ 中转API调用失败: {e}")
            # 返回错误响应
            yield ArkChatResponse(
                choices=[{
                    "message": {
                        "role": "assistant",
                        "content": f"抱歉，服务暂时不可用。错误信息：{str(e)}"
                    }
                }],
                usage={"total_tokens": 0}
            )
    
    async def arun(
        self,
        messages: List[ArkMessage],
        functions: Optional[List] = None,
        additional_system_prompts: Optional[List[str]] = None,
        **kwargs
    ) -> ArkChatResponse:
        """非流式对话"""
        
        # 转换消息格式
        openai_messages = self._convert_messages(messages, additional_system_prompts)
        
        # 构建请求参数
        request_params = {
            "model": self.model,
            "messages": openai_messages,
            "stream": False,
            "temperature": 0.7,
        }
        
        # 添加函数调用支持
        if functions:
            request_params["tools"] = self._convert_functions(functions)
            request_params["tool_choice"] = "auto"
        
        try:
            # 调用中转API
            response = await self.client.chat.completions.create(**request_params)
            
            # 转换响应格式
            return self._convert_response(response)
            
        except Exception as e:
            print(f"❌ 中转API调用失败: {e}")
            # 返回错误响应
            return ArkChatResponse(
                choices=[{
                    "message": {
                        "role": "assistant",
                        "content": f"抱歉，服务暂时不可用。错误信息：{str(e)}"
                    }
                }],
                usage={"total_tokens": 0}
            )
    
    def _convert_messages(
        self, 
        messages: List[ArkMessage], 
        additional_system_prompts: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """转换消息格式"""
        openai_messages = []
        
        # 添加额外的系统提示
        if additional_system_prompts:
            for prompt in additional_system_prompts:
                openai_messages.append({
                    "role": "system",
                    "content": prompt
                })
        
        # 转换原始消息
        for msg in messages:
            openai_messages.append({
                "role": msg.role,
                "content": msg.content
            })
        
        return openai_messages
    
    def _convert_functions(self, functions: List) -> List[Dict[str, Any]]:
        """转换函数格式为OpenAI工具格式"""
        tools = []
        
        for func in functions:
            # 这里需要根据实际的函数格式进行转换
            # 假设函数有name, description, parameters属性
            if hasattr(func, '__name__'):
                tool = {
                    "type": "function",
                    "function": {
                        "name": func.__name__,
                        "description": getattr(func, '__doc__', ''),
                        "parameters": {
                            "type": "object",
                            "properties": {},
                            "required": []
                        }
                    }
                }
                tools.append(tool)
        
        return tools
    
    def _convert_chunk_response(self, chunk) -> Optional[ArkChatCompletionChunk]:
        """转换流式响应"""
        if not chunk.choices:
            return None
        
        choice = chunk.choices[0]
        if not choice.delta:
            return None
        
        return ArkChatCompletionChunk(
            choices=[{
                "delta": {
                    "role": choice.delta.role,
                    "content": choice.delta.content or ""
                }
            }]
        )
    
    def _convert_response(self, response) -> ArkChatResponse:
        """转换非流式响应"""
        choice = response.choices[0]
        
        return ArkChatResponse(
            choices=[{
                "message": {
                    "role": choice.message.role,
                    "content": choice.message.content
                }
            }],
            usage={
                "total_tokens": response.usage.total_tokens if response.usage else 0
            }
        )


def get_llm_client(
    model: str,
    messages: List[ArkMessage],
    **kwargs
):
    """获取LLM客户端"""
    
    if config.use_proxy_api:
        # 使用中转API客户端
        return ProxyLLMClient()
    else:
        # 使用原版火山引擎客户端
        return BaseChatLanguageModel(
            model=model,
            messages=messages,
            **kwargs
        )
