# 智能客服助手 - 中转API配置指南

## 🎯 概述

本项目现已支持两种运行模式：
1. **🏔️ 火山引擎模式**（原版）- 使用火山引擎的完整服务栈
2. **🔄 中转API模式**（新增）- 使用ECOVAI等中转API服务

## 🔧 配置方式对比

### 环境变量 vs .env文件

| 方式 | 优点 | 缺点 | 推荐场景 |
|------|------|------|----------|
| **环境变量** | 安全性高，适合生产环境 | 配置繁琐，不易管理 | 生产部署 |
| **.env文件** | 配置简单，开发友好 | 需注意文件安全 | 本地开发 |

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装基础依赖
pip install uv
uv sync

# 或者手动安装
pip install python-dotenv openai
```

### 2. 配置.env文件

```bash
# 复制配置模板
cp .env.example .env

# 编辑配置文件
nano .env  # 或使用其他编辑器
```

### 3. 中转API配置示例

在`.env`文件中配置：

```bash
# ===========================================
# 中转API配置 (ECOVAI)
# ===========================================
ECOVAI_API_KEY=sk-YdpqauVUJQZjLwwwVJtZX9mttIKLF62CTYv4Ww0RL2x4cs56
ECOVAI_API_BASE=https://api.ecovai.cn/v1
ECOVAI_MODEL=DeepSeek-V3-0324

# ===========================================
# 通用配置
# ===========================================
LANGUAGE=zh
PORT=8080
```

### 4. 启动服务

```bash
# 使用启动脚本（推荐）
python start.py

# 或直接启动
python main.py
```

## 📋 配置项详解

### 必需配置

| 配置项 | 说明 | 示例值 |
|--------|------|--------|
| `ECOVAI_API_KEY` | 中转API密钥 | `sk-xxx...` |
| `ECOVAI_API_BASE` | API基础URL | `https://api.ecovai.cn/v1` |
| `ECOVAI_MODEL` | 模型名称 | `DeepSeek-V3-0324` |

### 可选配置

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `LANGUAGE` | 界面语言 | `zh` |
| `PORT` | 服务端口 | `8080` |
| `LOCAL_KNOWLEDGE_PATH` | 本地知识库路径 | `./data/knowledge` |
| `LOCAL_FAQ_PATH` | 本地FAQ路径 | `./data/faq` |

## 🔄 模式切换

### 切换到中转API模式

1. 在`.env`文件中设置`ECOVAI_API_KEY`
2. 重启服务

### 切换回火山引擎模式

1. 在`.env`文件中注释或删除`ECOVAI_API_KEY`
2. 配置火山引擎相关参数
3. 重启服务

## 📚 功能差异

### 中转API模式特点

✅ **支持的功能**：
- 智能对话
- 工具调用（订单查询、物流追踪、退款退货）
- 对话总结
- 质量检查
- 追问提示

⚠️ **限制**：
- 使用本地知识库（简化版）
- 本地FAQ存储
- 无云端同步功能

### 火山引擎模式特点

✅ **完整功能**：
- 所有中转API模式功能
- 云端知识库检索
- TOS对象存储
- 多租户支持

## 🛠️ 故障排除

### 常见问题

1. **配置验证失败**
   ```bash
   ❌ ECOVAI_API_KEY 未设置
   ```
   **解决方案**：检查`.env`文件中的API密钥配置

2. **依赖缺失**
   ```bash
   ❌ 缺少openai依赖
   ```
   **解决方案**：运行 `pip install openai`

3. **端口占用**
   ```bash
   ❌ 端口8080已被占用
   ```
   **解决方案**：在`.env`中修改`PORT=8081`

### 调试模式

启用详细日志：

```bash
# 在.env中添加
DEBUG=true
LOG_LEVEL=DEBUG
```

## 🔒 安全建议

### .env文件安全

1. **不要提交到版本控制**
   ```bash
   # 添加到.gitignore
   echo ".env" >> .gitignore
   ```

2. **设置文件权限**
   ```bash
   chmod 600 .env
   ```

3. **定期轮换密钥**
   - 定期更新API密钥
   - 监控API使用情况

## 📊 性能优化

### 中转API模式优化

1. **本地缓存**
   - 知识库结果缓存
   - 模型响应缓存

2. **并发控制**
   ```bash
   # 在.env中配置
   MAX_CONCURRENT_REQUESTS=10
   REQUEST_TIMEOUT=30
   ```

## 🔗 API兼容性

### 支持的中转API服务

| 服务商 | 配置示例 | 支持模型 |
|--------|----------|----------|
| **ECOVAI** | `https://api.ecovai.cn/v1` | DeepSeek-V3, GPT-4等 |
| **其他OpenAI兼容** | 修改`ECOVAI_API_BASE` | 根据服务商而定 |

### 自定义中转API

如需使用其他中转API服务，只需修改：

```bash
ECOVAI_API_BASE=https://your-api-provider.com/v1
ECOVAI_MODEL=your-model-name
```

## 📞 技术支持

如遇到问题，请检查：

1. ✅ 配置文件格式正确
2. ✅ API密钥有效
3. ✅ 网络连接正常
4. ✅ 依赖版本兼容

---

**💡 提示**：建议在开发环境使用.env文件，生产环境使用环境变量配置。
